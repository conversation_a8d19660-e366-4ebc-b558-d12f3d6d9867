import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useSession, signIn } from 'next-auth/react';
import { toast } from 'sonner';
import { useCart } from '@/context/cart-context';
import { SubscriptionInfoDialog } from '@/components/trial/subscription-info-dialog';
import { SubscriptionInfo, checkUserSubscription } from '@/src/services/subscriptionCheckService';
import { arePlansFromSameProduct, areExactSamePlan } from '@/src/utils/plan-utils';
import { useUser } from '@/context/user-context';
import { ensureUserExists } from '@/src/services/trialService';

interface SubscriptionButtonProps {
  plan: any;
  planCode: string;
  planName: string;
  planPrice: number;
  planDuration: 'monthly' | 'yearly' | 'trial';
  productSlug: string;
  productName: string;
  productLogo?: string | null;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  onPlanChange?: (existingPlan: any, newPlan: any, changeType: 'upgrade' | 'downgrade' | 'billing' | 'different') => void;
}

export function SubscriptionButton({
  plan,
  planCode,
  planName,
  planPrice,
  planDuration,
  productSlug,
  productName,
  productLogo,
  className,
  variant = 'default',
  size = 'default',
  onPlanChange
}: SubscriptionButtonProps) {
  const { items, addItem } = useCart();
  const [isSubscriptionInfoOpen, setIsSubscriptionInfoOpen] = useState(false);

  // Get session and user data
  const { data: session, status } = useSession();
  const { userId, userEmail, userName, isLoading: userContextLoading } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingUser, setIsCreatingUser] = useState(false);
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);

  // Log session state for debugging
  useEffect(() => {
    console.log(`🔍 SUBSCRIPTION BUTTON - Session status: ${status}, userId: ${session?.user?.id ? 'present' : 'missing'}`);

    // Check for existing subscription when component mounts and user is authenticated
    const checkExistingSubscription = async () => {
      if (status === 'authenticated' && session?.user?.id) {
        try {
          console.log(`🔍 SUBSCRIPTION BUTTON - Checking existing subscription on mount for plan ${planCode}`);
          const result = await checkUserSubscription(session.user.id, planCode);

          if (result.isSubscribed && result.subscription) {
            console.log(`🔍 SUBSCRIPTION BUTTON - Found existing subscription on mount`);
            setSubscription(result.subscription);
          }
        } catch (error) {
          console.error('Error checking subscription on mount:', error);
        }
      }
    };

    checkExistingSubscription();
  }, [session, status, planCode]);

  const handleSubscribeClick = async () => {
    console.log(`🔍 SUBSCRIPTION BUTTON - Button clicked for plan ${planCode}`);
    console.log(`🔍 SUBSCRIPTION BUTTON - Current session status: ${status}, userId: ${userId || 'missing'}`);

    if (!productSlug) {
      toast.error("Product information is not available");
      return;
    }

    // If the user already has a subscription, show the subscription info dialog
    if (subscription) {
      console.log(`🔍 SUBSCRIPTION BUTTON - User has existing subscription, showing subscription info dialog`);
      setIsSubscriptionInfoOpen(true);
      return;
    }

    // If user context is still loading, wait
    if (userContextLoading) {
      toast.info("Loading user information...");
      return;
    }

    // If user is not authenticated, trigger sign in
    if (status !== "authenticated") {
      try {
        // Store the plan info in localStorage before redirecting
        localStorage.setItem("pending_subscription_plan", planCode);
        localStorage.setItem("pending_subscription_product", planCode.split('_')[0]);
        localStorage.setItem("pending_subscription_slug", productSlug);
        localStorage.setItem("pending_subscription_name", planName);
        localStorage.setItem("pending_subscription_price", planPrice.toString());
        localStorage.setItem("pending_subscription_duration", planDuration);

        // Redirect to sign in page with return URL to come back to this product
        signIn("keycloak", { callbackUrl: `/products/${productSlug}` });
      } catch (error) {
        console.error("Error storing subscription data:", error);
        toast.error("Failed to store subscription data. Please try again.");
      }
      return;
    }

    // If authenticated but no userId, show error (only after user context has finished loading)
    if (!userId) {
      toast.error("User information not available. Please try again.");
      return;
    }

    // Check/create user in Lago before proceeding
    setIsCreatingUser(true);
    setIsLoading(true);

    try {
      const userExists = await ensureUserExists(
        userId,
        userEmail || undefined,
        userName || undefined
      );

      if (!userExists) {
        toast.error("Unable to create user account. Please try again later.");
        return;
      }

      // Check for existing subscription
      const result = await checkUserSubscription(userId, planCode);

      if (result.isSubscribed && result.subscription) {
        // User already has a subscription - show dialog
        console.log(`🔍 SUBSCRIPTION BUTTON - User already has subscription`);
        setSubscription(result.subscription);
        setIsSubscriptionInfoOpen(true);
        toast.info("You already have an active subscription for this product.");
      } else {
        // User doesn't have a subscription - check cart and handle upgrade/downgrade
        console.log(`🔍 SUBSCRIPTION BUTTON - User doesn't have subscription - checking cart`);

        // Create the new item object
        const newItem = {
          id: planName,
          name: planName,
          price: planPrice,
          planCode,
          quantity: 1,
          image: productLogo,
          slug: productSlug,
          productName,
          productLogo,
          planDuration
        };

        // First check if the exact same plan is already in cart
        const exactSamePlanInCart = items.find(item =>
          item.planCode && areExactSamePlan(item.planCode, planCode)
        );

        if (exactSamePlanInCart) {
          toast.info("This plan is already in your cart", {
            duration: 3000,
            action: {
              label: "View Cart",
              onClick: () => window.location.href = "/cart"
            }
          });
          return;
        }

        // Check if a different plan from the same product is in cart
        const differentPlanSameProduct = items.find(item =>
          item.planCode &&
          arePlansFromSameProduct(item.planCode, planCode) &&
          !areExactSamePlan(item.planCode, planCode)
        );

        if (differentPlanSameProduct) {
          console.log(`🔍 SUBSCRIPTION BUTTON - Found different plan from same product in cart: ${differentPlanSameProduct.planCode}`);

          // Determine if this is an upgrade, downgrade, or just a different plan
          let changeType: 'upgrade' | 'downgrade' | 'billing' | 'different' = 'different';

          // If same plan name but different billing period
          if (differentPlanSameProduct.id === planName) {
            // If switching from monthly to yearly, it's an upgrade
            if (differentPlanSameProduct.planDuration === 'monthly' && planDuration === 'yearly') {
              changeType = 'upgrade';
            } else {
              changeType = 'billing';
            }
          } else {
            // Different plan - determine if upgrade or downgrade based on price
            if (planPrice > differentPlanSameProduct.price) {
              changeType = 'upgrade';
            } else if (planPrice < differentPlanSameProduct.price) {
              changeType = 'downgrade';
            }
          }

          // If onPlanChange callback is provided, use it to show the upgrade/downgrade dialog
          if (onPlanChange) {
            try {
              // Add a small delay to ensure UI is responsive
              await new Promise(resolve => setTimeout(resolve, 50));
              onPlanChange(differentPlanSameProduct, newItem, changeType);
            } catch (error) {
              console.error('Error in plan change callback:', error);
              // Fallback if callback fails
              toast.info("You already have a plan for this product in your cart. Please remove it first or use the upgrade/downgrade option.", {
                duration: 5000,
                action: {
                  label: "View Cart",
                  onClick: () => window.location.href = "/cart"
                }
              });
            }
          } else {
            // Fallback if no callback provided
            toast.info("You already have a plan for this product in your cart. Please remove it first or use the upgrade/downgrade option.", {
              duration: 5000,
              action: {
                label: "View Cart",
                onClick: () => window.location.href = "/cart"
              }
            });
          }
        } else {
          // No conflicts, add to cart
          addItem(newItem);
          toast.success(`${planDuration === "trial" ? "Trial" : planName} plan added to cart`);
        }
      }
    } catch (error) {
      console.error("Error in subscription flow:", error);
      toast.error("Something went wrong. Please try again.");
    } finally {
      setIsCreatingUser(false);
      setIsLoading(false);
    }
  };

  return (
    <>
      <Button
        className={className}
        variant={variant}
        size={size}
        onClick={handleSubscribeClick}
        disabled={isLoading || userContextLoading}
      >
        {userContextLoading ? 'Loading...' : isLoading ? 'Checking...' : 'Subscribe Now'}
      </Button>

      {/* Subscription Info Dialog */}
      {subscription && (
        <SubscriptionInfoDialog
          open={isSubscriptionInfoOpen}
          onClose={() => setIsSubscriptionInfoOpen(false)}
          productName={productName}
          productSlug={productSlug}
          currentPlan={subscription}
          onViewPricing={() => setIsSubscriptionInfoOpen(false)}
          onGoToDashboard={() => window.location.href = `${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`}
        />
      )}
    </>
  );
}
