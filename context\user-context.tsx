"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useSession } from "next-auth/react";
import { getOrCreateUser } from "@/src/services/userService";

// NextAuth with Keycloak sets the ID to the Keycloak sub field
interface KeycloakUser {
  id: string;  // This is the sub from Keycloak
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

// Define user context type
interface UserContextType {
  userId: string | null;  // This will hold the Keycloak ID
  userEmail: string | null;
  userName: string | null;
  setUserInfo: (userId: string, email?: string, name?: string) => void;
  clearUserInfo: () => void;
  isLoading: boolean;
  isKeycloakUser: boolean;
}

// Create the context
const UserContext = createContext<UserContextType | undefined>(undefined);

// Provider component
export function UserProvider({ children }: { children: ReactNode }) {
  const { data: session, status } = useSession();
  const [userId, setUserId] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userName, setUserName] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isKeycloakUser, setIsKeycloakUser] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);
  
  // Load user info from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const savedUserId = localStorage.getItem('user_id');
        const savedUserEmail = localStorage.getItem('user_email');
        const savedUserName = localStorage.getItem('user_name');
        const savedIsKeycloak = localStorage.getItem('is_keycloak_user');
        
        if (savedUserId) setUserId(savedUserId);
        if (savedUserEmail) setUserEmail(savedUserEmail);
        if (savedUserName) setUserName(savedUserName);
        if (savedIsKeycloak) setIsKeycloakUser(savedIsKeycloak === 'true');
        
        console.log('Loaded user data from localStorage:', {
          id: savedUserId,
          email: savedUserEmail,
          name: savedUserName,
          isKeycloakUser: savedIsKeycloak
        });
      } catch (error) {
        console.error('Failed to load user data from localStorage:', error);
      } finally {
        setIsLoading(false);
      }
    }
  }, []);
  
  // When session changes, update user info
  useEffect(() => {
    const initializeUser = async () => {
      if (hasInitialized || status === 'loading') return;
      
      setIsLoading(true);
      try {
        // Log the full session for debugging
        console.log('🔍 Full NextAuth session:', JSON.stringify(session, null, 2));
        
        // Get Keycloak ID from session
        let keycloakId = null;
        let sessionUserEmail = null;
        let sessionUserName = null;
        
        if (session?.user) {
          console.log('📊 NextAuth session user object:', session.user);
          
          // In NextAuth with Keycloak, the ID is set to the Keycloak 'sub' value
          if (typeof session.user === 'object' && 'id' in session.user) {
            // NextAuth sets Keycloak's sub as the id field
            keycloakId = session.user.id;
            console.log('✅ Found Keycloak ID in session.user.id:', keycloakId);
            setIsKeycloakUser(true);
          }
          
          // Get email and name from session
          sessionUserEmail = session.user.email || null;
          sessionUserName = session.user.name || null;
        }
        
        // If we still don't have a Keycloak ID, try to find it in the token
        if (!keycloakId && session?.accessToken) {
          try {
            // If we have an access token, try to decode it to get the sub
            console.log('🔑 Attempting to parse access token');
            if (typeof session.accessToken === 'string') {
              const tokenParts = session.accessToken.split('.');
              if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload.sub) {
                  keycloakId = payload.sub;
                  console.log('✅ Found Keycloak ID (sub) in access token:', keycloakId);
                  setIsKeycloakUser(true);
                }
              }
            }
          } catch (error) {
            console.error('❌ Error parsing access token:', error);
          }
        }
        
        // Check in session for additional properties (some NextAuth configs include id_token)
        if (!keycloakId && session && typeof session === 'object') {
          try {
            // Access various possible token locations
            const idToken = (session as any).id_token || (session as any).idToken;
            if (idToken && typeof idToken === 'string') {
              const tokenParts = idToken.split('.');
              if (tokenParts.length === 3) {
                const payload = JSON.parse(atob(tokenParts[1]));
                if (payload.sub) {
                  keycloakId = payload.sub;
                  console.log('✅ Found Keycloak ID (sub) in ID token:', keycloakId);
                  setIsKeycloakUser(true);
                }
              }
            }
          } catch (error) {
            console.error('❌ Error checking session for ID token:', error);
          }
        }
        
        // Try to find the ID in raw tokens from localStorage/sessionStorage
        if (!keycloakId && typeof window !== 'undefined') {
          try {
            const token = localStorage.getItem('keycloak_token') || 
                        localStorage.getItem('token') || 
                        sessionStorage.getItem('keycloak_token') ||
                        sessionStorage.getItem('token');
            
            if (token) {
              try {
                const parts = token.split('.');
                if (parts.length === 3) {
                  const payload = JSON.parse(atob(parts[1]));
                  if (payload.sub) {
                    keycloakId = payload.sub;
                    console.log('✅ Found Keycloak ID in stored token:', keycloakId);
                    setIsKeycloakUser(true);
                  }
                }
              } catch (error) {
                console.error('❌ Error parsing stored token:', error);
              }
            }
          } catch (error) {
            console.error('❌ Error checking stored tokens:', error);
          }
        }
        
        // If we have a Keycloak ID, ensure the user exists in Lago
        if (keycloakId) {
          try {
            console.log(`🔄 Creating or verifying user in Lago with Keycloak ID: ${keycloakId}`);
            
            // This will either find the existing user or create a new one
            const userInLago = await getOrCreateUser({
              external_id: keycloakId,
              email: sessionUserEmail || undefined,
              name: sessionUserName || undefined
            });
            
            if (userInLago) {
              console.log(`✅ User exists/created in Lago with Keycloak ID: ${keycloakId}`);
            } else {
              console.warn(`⚠️ Failed to verify/create user in Lago with Keycloak ID: ${keycloakId}`);
            }
          } catch (error) {
            console.error('❌ Error verifying user in Lago:', error);
          }
        }
        
        // Update state and localStorage with Keycloak ID
        if (keycloakId) {
          setUserId(keycloakId);
          localStorage.setItem('user_id', keycloakId);
          localStorage.setItem('is_keycloak_user', 'true');
          
          if (sessionUserEmail) {
            setUserEmail(sessionUserEmail);
            localStorage.setItem('user_email', sessionUserEmail);
          }
          
          if (sessionUserName) {
            setUserName(sessionUserName);
            localStorage.setItem('user_name', sessionUserName);
          }
          
          console.log(`✅ Updated user context with Keycloak data: ID=${keycloakId}, Email=${sessionUserEmail}, Name=${sessionUserName}`);
        } else if (session?.user) {
          console.warn('⚠️ Session user object does not contain Keycloak ID:', session.user);
        }
        
        setHasInitialized(true);
      } catch (error) {
        console.error('❌ Error initializing user:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeUser();
  }, [session, status, hasInitialized]);
  
  // Add this somewhere in the useEffect block that handles user initialization
  useEffect(() => {
    // ... existing code ...
    
    // Add debug logging for Keycloak ID resolution
    if (userId) {
      console.log(`⚙️ USER CONTEXT - Current userId (Keycloak ID): ${userId}`);
      
      // Verify if userId is a valid Keycloak ID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      const isValidKeycloakId = uuidRegex.test(userId);
      
      if (!isValidKeycloakId) {
        console.warn(`⚠️ USER CONTEXT - Current userId doesn't match Keycloak UUID format: ${userId}`);
        
        // Try to find a Keycloak ID from session or local storage as fallback
        if (session?.user && (session.user as any).id) {
          const keycloakIdFromSession = (session.user as any).id;
          if (uuidRegex.test(keycloakIdFromSession)) {
            console.log(`🔄 USER CONTEXT - Using Keycloak ID from session instead: ${keycloakIdFromSession}`);
            setUserId(keycloakIdFromSession);
            setIsKeycloakUser(true);
            
            // Save for persistence
            try {
              localStorage.setItem('user_id', keycloakIdFromSession);
              localStorage.setItem('is_keycloak_user', 'true');
            } catch (e) {
              console.error('Error saving user ID to localStorage:', e);
            }
          }
        }
      } else {
        // It's a valid Keycloak ID
        setIsKeycloakUser(true);
        try {
          localStorage.setItem('is_keycloak_user', 'true');
        } catch (e) {
          console.error('Error saving Keycloak flag to localStorage:', e);
        }
      }
      
      // Check or create user in Lago for Lago features
      const initializeLagoUser = async () => {
        try {
          console.log(`🔄 USER CONTEXT - Initializing Lago user with Keycloak ID: ${userId}`);
          await getOrCreateUser({
            external_id: userId,
            email: userEmail || undefined,
            name: userName || undefined
          });
          console.log(`✅ USER CONTEXT - Lago user initialized with Keycloak ID: ${userId}`);
        } catch (error) {
          console.error('❌ USER CONTEXT - Error initializing Lago user:', error);
        }
      };
      
      // Initialize the Lago user
      initializeLagoUser();
    }
    
    // ... existing code ...
  }, [userId, userEmail, userName, session]);
  
  // Method to manually set user info
  const setUserInfo = (id: string, email?: string, name?: string) => {
    setUserId(id);
    localStorage.setItem('user_id', id);
    
    if (email) {
      setUserEmail(email);
      localStorage.setItem('user_email', email);
    }
    
    if (name) {
      setUserName(name);
      localStorage.setItem('user_name', name);
    }
    
    // Assume manually set IDs might be Keycloak IDs
    setIsKeycloakUser(true);
    localStorage.setItem('is_keycloak_user', 'true');
    
    console.log(`✅ Manually set user info: ID=${id}, Email=${email || 'not provided'}, Name=${name || 'not provided'}`);
  };
  
  // Method to clear user info
  const clearUserInfo = () => {
    setUserId(null);
    setUserEmail(null);
    setUserName(null);
    setIsKeycloakUser(false);
    localStorage.removeItem('user_id');
    localStorage.removeItem('user_email');
    localStorage.removeItem('user_name');
    localStorage.removeItem('is_keycloak_user');
    console.log('User info cleared');
  };
  
  return (
    <UserContext.Provider
      value={{
        userId,
        userEmail,
        userName,
        setUserInfo,
        clearUserInfo,
        isLoading,
        isKeycloakUser
      }}
    >
      {children}
    </UserContext.Provider>
  );
}

// Custom hook for using the user context
export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
} 